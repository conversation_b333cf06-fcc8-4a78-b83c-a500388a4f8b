(()=>{let e=null,t=!1,n=null,o=!1,i=null,r=0;function s(){const e=document.getElementById("prompt-manager-container");e&&(e.style.right="-400px",t=!1)}function c(t){var n,o;if(t.source===(null===(n=e)||void 0===n?void 0:n.contentWindow))switch(t.data.action){case"insert-prompt":!function(e){const t=a();if(t)try{if("true"===t.contentEditable){const t=window.getSelection(),n=t.getRangeAt(0);n.deleteContents(),n.insertNode(document.createTextNode(e)),n.collapse(!1),t.removeAllRanges(),t.addRange(n)}else{const n=t.selectionStart,o=t.selectionEnd,i=t.value,r=i.substring(0,n)+e+i.substring(o);t.value=r;const s=n+e.length;t.setSelectionRange(s,s)}t.dispatchEvent(new Event("input",{bubbles:!0})),t.focus(),s()}catch(e){console.error("Error inserting prompt:",e)}}(t.data.content);break;case"hide-panel":s();break;case"get-active-input":const n=a();e.contentWindow.postMessage({action:"active-input-info",hasActiveInput:!!n,inputType:null==n||null===(o=n.tagName)||void 0===o?void 0:o.toLowerCase()},"*")}}function a(){const e=document.activeElement;if(e&&("INPUT"===e.tagName||"TEXTAREA"===e.tagName||"true"===e.contentEditable))return e;const t=document.querySelectorAll('input[type="text"], input[type="email"], input[type="search"], textarea, [contenteditable="true"]');for(let e of t)if(null!==e.offsetParent)return e;return null}chrome.runtime.onMessage.addListener((n,o,i)=>{switch(n.action){case"toggle-panel":t?s():function(){e||function(){if(e)return e;const t=document.createElement("div");t.id="prompt-manager-container",t.style.cssText="\n    position: fixed;\n    top: 0;\n    right: -400px;\n    width: 400px;\n    height: 100vh;\n    z-index: 2147483647;\n    transition: right 0.3s ease-in-out;\n    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);\n    background: white;\n    border-left: 1px solid #e0e0e0;\n  ",e=document.createElement("iframe"),e.src=chrome.runtime.getURL("panel.html"),e.style.cssText="\n    width: 100%;\n    height: 100%;\n    border: none;\n    background: white;\n  ",t.appendChild(e),document.body.appendChild(t),window.addEventListener("message",c)}();const n=document.getElementById("prompt-manager-container");n&&(n.style.right="0px",t=!0,setTimeout(()=>{e&&e.contentWindow&&e.contentWindow.postMessage({action:"panel-shown",url:window.location.href},"*")},100))}(),i({success:!0});break;case"hide-panel":s(),i({success:!0})}}),document.addEventListener("click",e=>{if(t){const t=document.getElementById("prompt-manager-container");t&&!t.contains(e.target)&&s()}o&&(n&&n.contains(e.target)||g())}),document.addEventListener("keydown",e=>{"Escape"===e.key&&t&&s()}),document.addEventListener("input",function(e){const t=e.target;if(!(s=t)||"INPUT"!==s.tagName&&"TEXTAREA"!==s.tagName&&"true"!==s.contentEditable)return;var s;const c=t.value||t.textContent||"",a=t.selectionStart||0;if("#"===c[a-1])r=a-1,i=t,async function(e){try{const t=await chrome.runtime.sendMessage({action:"get-prompts"});if(l=u(t||[],""),0===l.length)return void g();d=0,function(e){n||(n=document.createElement("div"),n.className="prompt-autocomplete-popup",n.style.cssText="\n    position: absolute;\n    background: white;\n    border: 1px solid #d1d5db;\n    border-radius: 6px;\n    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\n    z-index: 2147483646;\n    max-width: 300px;\n    max-height: 200px;\n    overflow-y: auto;\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n    font-size: 14px;\n  ",function(e){if(!n)return;const t=e.getBoundingClientRect(),o=window.pageYOffset||document.documentElement.scrollTop,i=window.pageXOffset||document.documentElement.scrollLeft;n.style.left=t.left+i+"px",n.style.top=t.bottom+o+2+"px"}(e),document.body.appendChild(n))}(e),p(),o=!0}catch(e){console.error("Error showing autocomplete:",e)}}(t);else if(i===t&&o){const e=r;a>e?(m=c.substring(e+1,a),o&&chrome.runtime.sendMessage({action:"get-prompts"},e=>{l=u(e||[],m),0!==l.length?(d=0,p()):g()})):g()}var m}),document.addEventListener("keydown",function(e){if(o)switch(e.key){case"ArrowDown":e.preventDefault(),0!==l.length&&(d=(d+1)%l.length,p());break;case"ArrowUp":e.preventDefault(),0!==l.length&&(d=d>0?d-1:l.length-1,p());break;case"Enter":e.preventDefault(),m();break;case"Escape":e.preventDefault(),g()}});let l=[],d=0;function u(e,t){if(!t)return e.slice(0,5);const n=t.toLowerCase();return e.filter(e=>e.title.toLowerCase().includes(n)||e.description&&e.description.toLowerCase().includes(n)).slice(0,5)}function p(){n&&(n.innerHTML="",l.forEach((e,t)=>{const o=document.createElement("div");o.className="autocomplete-item "+(t===d?"selected":""),o.style.cssText=`\n      padding: 8px 12px;\n      cursor: pointer;\n      border-bottom: 1px solid #f3f4f6;\n      ${t===d?"background: #eff6ff;":""}\n    `,o.innerHTML=`\n      <div style="font-weight: 600; color: #111827; margin-bottom: 2px;">${e.title}</div>\n      ${e.description?`<div style="font-size: 12px; color: #6b7280;">${e.description}</div>`:""}\n    `,o.addEventListener("click",()=>{d=t,m()}),n.appendChild(o)}))}function m(){if(!i||0===l.length)return;const e=l[d];if(e){try{const t=i,n=t.value||t.textContent||"",o=t.selectionStart||n.length,s=n.substring(0,r),c=n.substring(o),a=s+e.content+c;"true"===t.contentEditable?t.textContent=a:t.value=a;const l=s.length+e.content.length;t.setSelectionRange&&t.setSelectionRange(l,l),t.dispatchEvent(new Event("input",{bubbles:!0})),t.focus(),async function(e){try{const t=(await chrome.runtime.sendMessage({action:"get-prompts"})||[]).find(t=>t.id===e);if(t){const e={...t,usageCount:(t.usageCount||0)+1,lastUsedAt:Date.now()};await chrome.runtime.sendMessage({action:"save-prompt",prompt:e})}}catch(e){console.error("Error updating prompt usage:",e)}}(e.id)}catch(e){console.error("Error inserting autocomplete item:",e)}g()}}function g(){n&&(document.body.removeChild(n),n=null),o=!1,i=null,l=[],d=0}window.addEventListener("beforeunload",()=>{mouseEdgeTimer&&clearTimeout(mouseEdgeTimer),n&&document.body.removeChild(n)})})();